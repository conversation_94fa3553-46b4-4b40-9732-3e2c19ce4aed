import React, { useState, useEffect } from 'react'
import { useParams, useHistory } from 'react-router-dom'
import { Button, Steps, Card, Tag, message, Spin, Modal, Dropdown, Menu } from 'antd'

import IconStatus0 from 'ROOT/assets/images/icon_status0.png'
import IconStatus1 from 'ROOT/assets/images/icon_status1.png'
import IconStatus2 from 'ROOT/assets/images/icon_status2.png'
import IconStatus3 from 'ROOT/assets/images/icon_status3.png'
import IconStatus4 from 'ROOT/assets/images/icon_status4.png'
import { css } from 'emotion'
import moment from 'moment'
import service from 'ROOT/service'
import './index.css'

const { Step } = Steps

const AgendaManage = () => {
  const { id } = useParams()
  const history = useHistory()
  const [loading, setLoading] = useState(false)
  const [meetingData, setMeetingData] = useState(null)
  const [currentStep, setCurrentStep] = useState(0)
  const isconStatus = [
    IconStatus0,
    IconStatus1,
    IconStatus2,
    IconStatus3,
    IconStatus4,
  ]
  // 获取会议详情
  useEffect(() => {
    const fetchMeetingData = async () => {
      if (id) {
        try {
          setLoading(true)
          const response = await service.getMeetingDetail({ meetingId: id })
          console.log('会议详情:', response)
          setMeetingData(response)

          // 根据会议状态设置当前步骤
          if (response && response.status !== undefined) {
            setCurrentStep(response.status)
          }
        } catch (error) {
          console.error('获取会议详情失败:', error)
          message.error('获取会议详情失败')
        } finally {
          setLoading(false)
        }
      }
    }
    fetchMeetingData()
  }, [id])

  // 议题状态映射
  const getTopicStatus = (status) => {
    const statusMap = {
      0: { text: '未开始', color: 'default' },
      1: { text: '请候场', color: 'orange' },
      2: { text: '进行中', color: 'processing' },
      3: { text: '已结束', color: 'success' }
    }
    return statusMap[status] || statusMap[0]
  }

  // 渲染会议流程步骤
  const renderMeetingSteps = () => {
    const steps = [
      { key: 0, text: '未开始' },
      { key: 1, text: '请候场' },
      { key: 2, text: '进行中' },
      { key: 3, text: '已结束' }
    ]

    return (
      <div className={css`
        display: flex;
        flex-direction: column;
        gap: 8px;
      `}>
        {steps.map((step) => {
          const isActive = currentStep === step.key
          const isCompleted = currentStep > step.key

          return (
            <div key={step.key} className={css`
              display: flex;
              align-items: center;
              gap: 8px;
            `}>
              <div className={css`
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: ${isActive ? '#1890ff' : isCompleted ? '#52c41a' : '#d9d9d9'};
                flex-shrink: 0;
              `} />
              <div className={css`
                font-size: 14px;
                color: ${isActive ? '#1890ff' : isCompleted ? '#52c41a' : '#999'};
                font-weight: ${isActive ? '500' : '400'};
              `}>
                {step.text}
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  // 渲染会议基本信息
  const renderMeetingInfo = () => {
    if (!meetingData) return null

    return (
      <div>
        <div className={css`
          margin-bottom: 16px;
        `}>
          <div className={css`
            font-size: 16px;
            font-weight: 500;
            color: #5C626B;
          `}>{meetingData.name || '-'}</div>
        </div>

        <div className={css`
          margin-bottom: 10px;
          display: flex;
        `}>
          <div className={css`
            color: #666;
            margin-bottom: 8px;
            font-size: 14px;
          `}>开始时间：</div>
          <div className={css`
            color: #5C626B;
          `}>{meetingData.startTime ? moment(meetingData.startTime).format('YYYY年MM月DD日') : '-'}</div>
        </div>

        <div className={css`
          margin-bottom: 10px;
          display: flex;
        `}>
          <div className={css`
            color: #666;
            margin-bottom: 8px;
            font-size: 14px;
          `}>会议类型：</div>
          <div className={css`
            color: #5C626B;
          `}>{meetingData.meetingType || '-'}</div>
        </div>

        <div className={css`
          margin-bottom: 10px;
          display: flex;
        `}>
          <div className={css`
            color: #666;
            margin-bottom: 8px;
            font-size: 14px;
          `}>会议室：</div>
          <div className={css`
            color: #5C626B;
          `}>{meetingData.meetingRoom || '-'}</div>
        </div>

        <div className={css`
          margin-bottom: 10px;
          display: flex;
        `}>
          <div className={css`
            color: #666;
            margin-bottom: 8px;
            font-size: 14px;
          `}>会议日期：</div>
          <div className={css`
            color: #5C626B;
          `}>{meetingData.startTime ? moment(meetingData.startTime).format('YYYY年MM月DD日') : '-'}</div>
        </div>

        <div className={css`
          margin-bottom: 10px;
          display: flex;
        `}>
          <div className={css`
            color: #666;
            margin-bottom: 8px;
            font-size: 14px;
          `}>参会领导：</div>
          <div className={css`
            color: #5C626B;
          `}>
            {meetingData.leaderList && meetingData.leaderList.length > 0
              ? meetingData.leaderList.map(leader => leader.name).join('、')
              : '-'
            }
          </div>
        </div>
      </div>
    )
  }

  // 更新议题状态
  const updateTopicStatus = async (topicId, newStatus) => {
    try {
      setLoading(true)
      await service.editMeeting({
        topicId: topicId,
        status: newStatus
      })
      message.success('议题状态更新成功')

      // 重新获取会议数据
      const response = await service.getMeetingDetail({ meetingId: id })
      setMeetingData(response)

      // 更新会议流程状态
      if (response && response.status !== undefined) {
        setCurrentStep(response.status)
      }
    } catch (error) {
      console.error('更新议题状态失败:', error)
      message.error('更新议题状态失败')
    } finally {
      setLoading(false)
    }
  }

  // 渲染议题卡片
  const renderTopicCard = (topic, index) => {
    const status = getTopicStatus(topic.status || 0)
    const isActive = topic.topicsStatus === 2 // 进行中状态
    const isCompleted = topic.topicsStatus === 3 // 已结束状态

    return (
      <div
        key={index}
        className={css`
          position: relative;
          margin-bottom: 30px;
          padding-left: 32px;
        `}
      >
        {/* 时间线连接线 */}
        <div className={css`
            position: absolute;
            left: 15px;
            top: 12px;
            bottom: -40px;
            width: 2px;
            background: #e8e8e8;
          `} />

        {/* 状态指示器 */}
        <div className={css`
          position: absolute;
          left: 6px;
          top: 4px;
          width: 20px;
          height: 20px;
          z-index: 2;
        `}>
          <img
            src={isconStatus[topic.topicsStatus || 0]}
            alt="Status"
            style={{ width: '20px', height: '20px' }}
          />
        </div>
        <div className={css`
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            flex-direction: row;
            flex-wrap: nowrap;
            align-content: center;
            justify-content: space-between;
          `}>
          <div className={css`
            display: flex;
            align-items: center;
            gap: 8px;
          `}>
            {['未开始', '请候场', '进行中', '已结束'].map((step, index) => (
              <div key={index} className={css`
                display: flex;
                align-items: center;
                gap: 8px;
                font-weight: ${index === topic.topicsStatus ? '500' : '400'};
              `}>
                {index !== 0 && <div className={css`
                    color: rgba(203, 207, 214, 1);
                    font-family: '宋体';
                    font-weight: 600;
                  `}>&gt;&gt;</div>}
                <Button className={css`
                  border-radius: 16px;  
                `}
                  type={index < topic.topicsStatus ? 'disabled' : index == topic.topicsStatus ? 'primary' : 'default'}>{step}</Button>
              </div>
            ))}
          </div>
          {topic.topicsStatus === 0 && <Button type='default' className={css`
            border-radius: 5px;  
          `}>取消议题</Button>}
        </div>
        <Card
          className={css`
            border: ${isActive ? '2px solid #1890ff' : '1px solid #e8e8e8'};
            border-radius: 8px;
            box-shadow: ${isActive ? '0 4px 12px rgba(16, 144, 255, 0.15)' : '0 2px 8px rgba(0, 0, 0, 0.06)'};
            position: relative;

            .ant-card-body {
              padding: 20px;
            }
          `}
        >
          <div className={css`
            margin-bottom: 16px;
          `}>
            <span className={css`
              font-weight: 500;
              margin-right: 8px;
              font-size: 16px;
              color: #262A30;
            `}><Tag color={status.color}>议题{index+1}</Tag>议题名称：</span>
            <span className={css`
              font-weight: 500;
              font-size: 16px;
              color: #262A30;
              `}>{topic.topicsName || ''}</span>
          </div>

          {/* 议题详细信息 */}
          <div className={css`
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 16px;
            color: #666;
            font-size: 14px;
            margin-bottom: 12px;
          `}>
            <div>
              <span className={css`font-weight: 500;`}>议题时长：</span>
              <span className={css`color: rgba(17, 94, 210, 1);`}>{topic.topicsDuration || 30}分钟</span>
            </div>
            <div>
              <span className={css`font-weight: 500;`}>议题开始时间：</span>
              <span>{topic.topicsStartTime ? moment(topic.topicsStartTime).format('YYYY年MM月DD日 HH:mm') : '2025年12月12日 12:33'}</span>
            </div>
            <div>
              <span className={css`font-weight: 500;`}>汇报人：</span>
              <span>{topic.reportList && topic.reportList.length > 0 ? topic.reportList.map(r => r.name).join('、') : '张彬'}</span>
            </div>
          </div>

          <div className={css`
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 16px;
            color: #666;
            font-size: 14px;
          `}>
            <div>
              <span className={css`font-weight: 500;`}>列席联系人：</span>
              <span>{topic.attendanceList && topic.attendanceList.length > 0 ? topic.attendanceList.map(a => a.name).join('、') : '张彬'}</span>
            </div>
            <div>
              <span className={css`font-weight: 500;`}>列席单位：</span>
              <span>{topic.attendanceDept && topic.attendanceDept.length > 0 ? topic.attendanceDept.map(d => d.deptName || d.name).join('、') : '内容内容'}</span>
            </div>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <div className={css`
      margin:-68px -58px;
      background: #f7f8f9;
    `}>
      <Spin spinning={loading}>

        {/* 左右布局主体 */}
        <div className={css`
          display: flex;
          gap: 16px;
          height: calc(100vh - 20px);
        `}>
          {/* 左侧：会议基本信息 */}
          <div className={css`
            flex: 0 0 300px;
            background: #fff;
            padding: 16px;
            overflow-y: auto;
          `}>
            {renderMeetingInfo()}
          </div>

          {/* 右侧：议题流程 */}
          <div className={css`
            flex: 1;
            padding: 16px;
            overflow-y: auto;
          `}>
            <h3 className={css`
              margin: 0 0 16px 0;
              font-size: 16px;
              font-weight: 600;
            `}>会议流程</h3>

            {meetingData && meetingData.topicsList && meetingData.topicsList.length > 0 ? (
              meetingData.topicsList.map((topic, index) => renderTopicCard(topic, index))
            ) : (
              <div className={css`
                text-align: center;
                color: #999;
                padding: 40px;
              `}>
                暂无议题
              </div>
            )}
          </div>
        </div>
      </Spin>
    </div>
  )
}

export default AgendaManage
