import React, { useEffect, useState } from 'react'
import { Table, Button, Spin, Modal, message } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import { css } from 'emotion'
import { useHistory } from 'react-router-dom'
import PageHeader from '../components/pageHeader'
import Search from '../components/search'
import service from 'ROOT/service'
import moment from 'moment'
import './index.less'

const MeetManage = () => {
  const history = useHistory()
  const [loading, setLoading] = useState(false)
  const [searchValues, setSearchValues] = useState({})
  const [dataSource, setDataSource] = useState([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  })


  const columns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
    {
      title: '会议日期', dataIndex: 'meetingDate', key: 'meetingDate', align: 'center', render: (text) => {
        // 时间戳转时间
        return moment(text).format('YYYY-MM-DD')
      }
    },
    { title: '会议名称', dataIndex: 'name', key: 'name', align: 'center' },
    {
      title: '会议状态', dataIndex: 'meetingStatus', key: 'meetingStatus', align: 'center', render: (text) => {
        // 状态码转文字
        const statusMap = {
          0: '未开始',
          1: '准备中',
          2: '进行中',
          3: '已结束',
        }
        return <div className={css`
        :before {
          content: '';
          display: inline-block;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          margin-right: 5px;
          background-color: ${text === 0 ? '#CBCFD6' : text === 1 ? '#DD5D1D' : text === 2 ? '#2F7FFF' : '#CBCFD6'};
        }
        `}>{statusMap[text] || '未知'}</div>
      }
    },
    { title: '会议类型', dataIndex: 'meetingType', key: 'meetingType', align: 'center' },
    { title: '会议室', dataIndex: 'meetingRoom', key: 'meetingRoom', align: 'center' },
    {
      title: '开始时间', dataIndex: 'startTime', key: 'startTime', align: 'center', render: (text) => {
        // 时间戳转时间
        return moment(text).format('YYYY-MM-DD HH:mm')
      }
    },
    {
      title: '参会领导', dataIndex: 'leaderList', key: 'leaderList', align: 'center', render: (text) => {
        // 处理参会领导
        if (text.length > 3) {
          return text.slice(0, 3).map(item => item.name).join(',') + '...'
        } else {
          return text.map(item => item.name).join(',')
        }
      }
    },
    {
      title: '参会经办', dataIndex: 'operatorList', key: 'operatorList', align: 'center', render: (text) => {
        // 处理参会经办
        if (text.length > 3) {
          return text.slice(0, 3).map(item => item.name).join(',') + '...'
        } else {
          return text.map(item => item.name).join(',')
        }
      }
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 260,
      render: (_, record) => {
        return (
          record.update && <div>
            <Button
              type="link"
              onClick={() => handleAgenda(record)}
            >
              议题管理
            </Button>
            <Button
              type="link"
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
            <Button
              type="link"
              danger
              onClick={() => handleDelete(record)}
            >
              删除
            </Button>
          </div>
        )
      }
    }
  ]
  const handleAgenda = (record) => {
    history.push(`/web/MeetManage/agenda/${record.id}`)
  }
  // 获取会议列表数据
  const getData = async (searchParams = null, paginationParams = null) => {
    try {
      setLoading(true)
      // 如果没有传入搜索参数，使用当前的 searchValues
      const currentSearchValues = searchParams !== null ? searchParams : searchValues
      // 如果没有传入分页参数，使用当前的 pagination
      const currentPagination = paginationParams || pagination
      // 构建请求参数，特别处理日期字段
      const requestParams = {
        pageIndex: currentPagination.current,
        pageSize: currentPagination.pageSize,
      }

      // 逐个处理搜索参数，避免传递无效值
      Object.keys(currentSearchValues).forEach(key => {
        const value = currentSearchValues[key]
        if (value !== undefined && value !== null && value !== '') {
          if (key === 'meetingDate') {
            // 特殊处理日期字段
            try {
              if (moment.isMoment(value)) {
                requestParams[key] = value.valueOf()
              } else if (typeof value === 'string' || typeof value === 'number') {
                requestParams[key] = moment(value).valueOf()
              }
            } catch (error) {
              console.warn('日期转换失败:', value, error)
              // 日期转换失败时不添加该字段
            }
          } else {
            requestParams[key] = value
          }
        }
      })

      const response = await service.getMeetingList(requestParams)

      if (response && response.list) {
        // 添加序号
        const dataWithIndex = response.list.map((item, index) => ({
          ...item,
          index: (currentPagination.current - 1) * currentPagination.pageSize + index + 1,
        }))

        setDataSource(dataWithIndex)
        setPagination(prev => ({
          ...prev,
          total: response.total || 0,
        }))
      } else {
        setDataSource([])
        setPagination(prev => ({
          ...prev,
          total: 0,
        }))
      }
    } catch (error) {
      console.error('获取会议列表失败:', error)
      message.error('获取会议列表失败')
      setDataSource([])
    } finally {
      setLoading(false)
    }
  }

  // 处理编辑
  const handleEdit = (record) => {
    history.push(`/web/MeetManage/meetDetail/${record.id}`)
  }

  // 处理删除
  const handleDelete = (record) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除会议"${record.name}"吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true)
          await service.deleteMeeting({ meetingId: record.id })
          message.success('删除成功')
          getData() // 重新获取数据
        } catch (error) {
          console.error('删除失败:', error)
          message.error(`删除失败: ${error.msg || '未知错误'}`)
        } finally {
          setLoading(false)
        }
      },
    })
  }

  // 处理创建会议
  const handleCreate = () => {
    history.push('/web/MeetManage/meetDetail/new')
  }
  // 处理搜索提交
  const onSubmit = (values) => {
    setSearchValues(values)
    const newPagination = { ...pagination, current: 1 }
    setPagination(newPagination)
    getData(values, newPagination)
  }

  // 处理搜索重置
  const onReset = () => {
    // 重置时使用空对象
    const emptyValues = {}
    setSearchValues(emptyValues)
    const newPagination = { ...pagination, current: 1 }
    setPagination(newPagination)
    getData(emptyValues, newPagination) // 直接传递空对象和新的分页参数
  }

  // 处理分页变化
  const handleTableChange = (paginationInfo) => {
    const newPagination = {
      ...pagination,
      current: paginationInfo.current,
      pageSize: paginationInfo.pageSize,
    }
    setPagination(newPagination)
    // 分页变化时传入新的分页参数
    getData(null, newPagination)
  }

  useEffect(() => {
    // 初始加载时获取数据
    getData()
  }, [])
  return (
    <div className={css`
      margin-top: -86px;
      margin-left: -59px;
      margin-right: -59px;
      padding: 20px;
    `}>
      <Spin
        spinning={loading}
        style={{ height: '100vh', overflow: 'hidden', maxHeight: 'initial' }}
      >
        <PageHeader
          title={'会议管理'}
          hasBack={true}
          extra={
            <Button icon={<PlusOutlined />} type='primary' onClick={handleCreate}>
              创建会议
            </Button>
          }
        />
        <div
          className={css`
          overflow: auto;
        `}
        >
          <div className="box meetManage-search-table">
            <Search onSubmit={onSubmit} onReset={onReset} />
            <Table
              columns={columns}
              rowKey="id"
              dataSource={dataSource}
              pagination={{
                ...pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
              }}
              onChange={handleTableChange}
            />
          </div>
        </div>
      </Spin>
    </div>

  )
}

export default MeetManage
