import React from 'react'
import {
  SchemaForm,
  Submit,
  Reset,
  SchemaMarkupField as Field,
  FormButtonGroup,
  FormMegaLayout,
} from '@formily/antd'
import {RedoOutlined} from '@ant-design/icons'
import { css } from 'emotion'
import { Input, DatePicker, Select } from 'antd'

const { RangePicker } = DatePicker

const Search = props => {
  const { type = 0, onSubmit = () => {}, onReset = () => {} } = props

  const handleReset = () => {
    onReset()
  }

  const handleSubmit = (values) => {
    onSubmit(values)
  }

  return (
    <div
      className={css`
        .ant-form.ant-form-inline {
          padding: 16px 0 !important;
          display: flex !important;

          .mega-layout-container {
            flex: 1 !important;

            .mega-layout-container-content {
              display: flex !important;
              flex-wrap: wrap !important;

              .mega-layout-item {
                width: 320px !important;
                margin: 0 16px 16px 0 !important;
              }
            }
          }

          .is-inline {
            max-width: 138px;
            .inline-view {
              display: flex;
              align-items: center;
            }
          }
        }
      `}
    >
      <SchemaForm
        inline
        components={{ Input, RangePicker,DatePicker, Select }}
        onSubmit={handleSubmit}
        onReset={handleReset}
        initialValues={{}}
      >
        <FormMegaLayout inline inset>
          <Field
            type="string"
            name={'name'}
            title={'会议名称'}
            x-component="Input"
            x-component-props={{ placeholder: '请输入会议名称', allowClear: true  }}
          />
          <Field
            type="string"
            name="meetingDate"
            title="会议时间"
            x-component="DatePicker"
            x-component-props={{ placeholder: '请选择会议时间', allowClear: true }}
          />
          <Field
            type="string"
            name="meetingType"
            title="会议类型"
            x-component="Input"
            x-component-props={{ placeholder: '请输入会议类型', allowClear: true }}
          />
          <Field
            type="string"
            name="operatorName"
            title="经办人"
            x-component="Input"
            x-component-props={{ placeholder: '请输入经办人', allowClear: true }}
          />
        </FormMegaLayout>
        <FormButtonGroup>
          <Reset><RedoOutlined /></Reset>
          <Submit>搜索</Submit>
        </FormButtonGroup>
      </SchemaForm>
    </div>
  )
}

export default Search
